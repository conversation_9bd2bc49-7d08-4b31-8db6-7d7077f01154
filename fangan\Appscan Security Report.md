# Web Application Report

This report includes important security information about your web application.

# Security Report

This report was created by HCL AppScan Standard 10.0.7  Scan started: 9/8/2025 10:27:36 AM

<div align="right">第1页</div>

# Table of Contents

# Introduction

Introduction- General Information- Login Settings

# Summary

Summary- Issue Types- Vulnerable URLs- Fix Recommendations- Security Risks- Causes- WASC Threat Classification

# Issues Sorted by Issue Type

Issues Sorted by Issue Type- Password Parameter in Query 2- Weak Signature in JWT 1- Credit Card Number Pattern Found (American Express) Over Unencrypted Connection 1- Credit Card Number Pattern Found (Diners Club) Over Unencrypted Connection 1- Credit Card Number Pattern Found (Visa) Over Unencrypted Connection 1- Missing "Content- Security- Policy" header 1- Missing or insecure "X- Content- Type- Options" header 1- Missing or insecure "X- XSS- Protection" header 1- Sensitive Authentication(Basic) Information Leak 1- Client- Side (JavaScript) Cookie References 3- Email Address Pattern Found 6- Integer Overflow 3- Internal IP Disclosure Pattern Found 6- Missing "Referrer policy" Security Header 1- Possible Server Path Disclosure Pattern Found 3- Unsanitized user input reflected in JSON 10

<div align="right">第2页</div>

# Introduction

This report contains the results of a web application security scan performed by HCL AppScan Standard.

High severity issues: 2  Medium severity issues: 1  Low severity issues: 7  Informational severity issues: 32  Total security issues included in the report: 42  Total security issues discovered in the scan: 42

# General Information

General InformationScan file name: Appscan  Scan started: 9/8/2025 10:27:36 AM  Test policy: Default  Test optimization level: Fast  Host **************  Port 80  Operating system: Unknown  Web server: Unknown  Application server: Any

# Login Settings

Login SettingsLogin method: Automatic  Concurrent logins: Enabled  In- session detection: Enabled  In- session pattern: 200\s+OK  Tracked or session ID cookies:  Tracked or session ID parameters:  Login sequence: http://**************/appss  http://**************/console/api/system- features  http://**************/console/api/setup  http://**************/signin?_rsc=13qlv  http://**************/reset- password?_rsc=1x4yu

<div align="right">第3页</div>

http://**************/install?_rsc=1x4yu  http://**************/console/api/login  http://**************/console/api/apps?page=1&limit=30&name=  http://**************/console/api/account/profile  http://**************/console/api/workspaces/current  http://**************/console/api/version?current_version=1.6.0  http://**************/apps?_rsc=tg7gv  http://**************/console/api/workspaces  http://**************/console/api/workspaces/current/models/model- types/1lm  http://**************/console/api/workspaces/current/models/model- types/text- embedding  http://**************/console/api/workspaces/current/models/model- types/rerank  http://**************/console/api/apps?page=1&limit=30&name=&is_created_by_me=false  http://**************/console/api/workspaces/current/model- providers  http://**************/console/api/datasets/retrieval- setting  http://**************/console/api/workspaces/current/plugin/permission/fetch  http://**************/console/api/tags?type=app  http://**************/console/api/features  http://**************/explore/apps?_rsc=1pj41  http://**************/datasets?_rsc=1pj41  http://**************/tools?_rsc=1pj41  http://**************/plugins?_rsc=1pj41  http://**************/apps?_rsc=tg7gv  http://**************/console/api/workspaces/current/plugin/tasks?page=1&page_size=100  http://**************/app/xsale8be5- 3391- 4af1- bebc8e8b951ccfdf/workflow?_rsc=5ibf

<div align="right">第4页</div>

# Summary

# Issue Types

![](images/f29a1e30199f5e2ba86b3db6035bcd8d0fd57404b41fa319dbe8bcd15ddf213e.jpg)

# Vulnerable URLs

![](images/9c3848e720ce37671f9ae3b46d7f73fe9c31b2c9fa8867d638672e1197d58666.jpg)

<div align="right">第5页</div>

<table><tr><td>1</td><td>http://**************/_next/static/chunks/6133-2a596c0b72baec18.js</td><td>1</td><td></td></tr><tr><td>1</td><td>http://**************/_next/static/chunks/8717-023615e5fc4f49ab.js</td><td>1</td><td></td></tr><tr><td>1</td><td>http://**************/_next/static/chunks/app/(commonLayout)/layout-555df4ce5c8df4 04.js</td><td>1</td><td></td></tr><tr><td>1</td><td>http://**************/console/api/account/profile</td><td>2</td><td></td></tr><tr><td>1</td><td>http://**************/console/api/workspaces/current/plugin/tasks</td><td>5</td><td></td></tr><tr><td>1</td><td>http://**************/console/api/apps</td><td>6</td><td></td></tr><tr><td>1</td><td>http://**************/console/api/datasets/api-base-info</td><td>1</td><td></td></tr><tr><td>1</td><td>http://**************/console/api/datasets/external-knowledge-api</td><td>1</td><td></td></tr><tr><td>1</td><td>http://**************/console/api/workspaces/current/model-providers</td><td>1</td><td></td></tr><tr><td>1</td><td>http://**************/_next/static/chunks/75146d7d-4fef07hfe49f0ba6.js</td><td>1</td><td></td></tr><tr><td>1</td><td>http://**************/_next/static/chunks/8058-427e825bd255e5dd.js</td><td>1</td><td></td></tr><tr><td>1</td><td>http://**************/_next/static/chunks/app/(commonLayout)/datasets/page-cb76e2f 5142ba880.js</td><td>1</td><td></td></tr><tr><td>1</td><td>http://**************/console/api/login</td><td>2</td><td></td></tr></table>

# Fix Recommendations 14

<table><tr><td colspan="2">Remediation Task</td><td>Number of Issues</td></tr><tr><td>H</td><td>Always use SSL and POST (body) parameters when sending sensitive information.</td><td>2</td></tr><tr><td>M</td><td>It is necessary to keep a while list of authorised algorithms on the application side and to dismiss all tokens having a signature algorithm that is different from the one authorised on the server</td><td>1</td></tr><tr><td>L</td><td>Config your server to use the &quot;Content-Security-Policy&quot; header with secure policies</td><td>1</td></tr><tr><td>L</td><td>Config your server to use the &quot;Referrer Policy&quot; header with secure policies</td><td>1</td></tr><tr><td>L</td><td>Config your server to use the &quot;X-Content-Type-Options&quot; header with &quot;nosniff&quot; value</td><td>1</td></tr><tr><td>L</td><td>Config your server to use the &quot;X-XSS-Protection&quot; header with value &quot;1&quot; (enabled)</td><td>1</td></tr><tr><td>L</td><td>Download the relevant security patch for your web server or web application.</td><td>3</td></tr><tr><td>L</td><td>Protect sensitive information and avoid accidental leaks</td><td>1</td></tr><tr><td>L</td><td>Remove business and security logic from the client side</td><td>3</td></tr><tr><td>L</td><td>Remove credit card numbers from your website</td><td>3</td></tr><tr><td>L</td><td>Remove e-mail addresses from the website</td><td>6</td></tr><tr><td>L</td><td>Remove internal IP addresses from your website</td><td>6</td></tr><tr><td>L</td><td>Review possible solutions for hazardous character injection</td><td>10</td></tr><tr><td>L</td><td>Verify that parameter values are in their expected ranges and types. Do not output debugging error messages and exceptions</td><td>3</td></tr></table>

# Security Risks 9

<table><tr><td>Risk</td><td>Number of Issues</td></tr><tr><td>It may be possible to steal sensitive data such as usernames and passwords that are</td><td>2</td></tr></table>

<div align="right">第6页</div>

<table><tr><td colspan="3">sent in the query string</td></tr><tr><td>M</td><td>It might be possible to escalate user privileges and gain administrative permissions over the web application</td><td>1</td></tr><tr><td>M</td><td>It may be possible to bypass the web application&#x27;s authentication mechanism</td><td>1</td></tr><tr><td>L</td><td>It is possible to gather sensitive information about the web application such as usernames, passwords, machine name and/or sensitive file locations</td><td>20</td></tr><tr><td>L</td><td>It is possible to persuade a naive user to supply sensitive information such as username, password, credit card number, social security number etc.</td><td>4</td></tr><tr><td>1</td><td>The worst case scenario for this attack depends on the context and role of the cookies that are created at the client side</td><td>3</td></tr><tr><td>1</td><td>It is possible to gather sensitive debugging information</td><td>3</td></tr><tr><td>1</td><td>It is possible to retrieve the absolute path of the web server installation, which might help an attacker to develop further attacks and to gain information about the file system structure of the web application</td><td>3</td></tr><tr><td>1</td><td>It may be possible to steal or manipulate customer session and cookies, which might be used to impersonate a legitimate user, allowing the hacker to view or alter user records, and to perform transactions as that user</td><td>10</td></tr></table>

# Causes

<table><tr><td>Cause</td><td>Number of Issues</td><td></td></tr><tr><td>H</td><td>SSL (Secure Socket Layer) provides data confidentiality and integrity to HTTP. By encrypting HTTP messages, SSL protects from attackers eavesdropping or altering message contents. Login pages should always employ SSL to protect the user name and password while they are in transit from the client to the server. Lack of SSL use exposes the user credentials as clear text during transmission to the server and thus makes the credentials susceptible to eavesdropping.</td><td></td></tr><tr><td>M</td><td>The application server has no proper verification for the requests with JWT header having &quot;none&quot; algorithm.</td><td></td></tr><tr><td>L</td><td>Insecure web application programming or configuration</td><td>20</td></tr><tr><td>I</td><td>Cookies are created at the client side</td><td>3</td></tr><tr><td>1</td><td>An Integer Overflow (or wraparound) occurs when a value that is too large is stored (larger than the maximum value the variable can hold) in an integer data type (including byte, short, long, and other types). The most significant bits of the integer are lost, and the remaining value is relative to the minimum value (either 0 or very negative value for signed types).</td><td></td></tr><tr><td>1</td><td>Latest patches or hotfixes for 3rd. party products were not installed</td><td>3</td></tr><tr><td>1</td><td>Cross-site scripting (XSS) vulnerabilities arise when an attacker sends malicious code to the victim&#x27;s browser, mostly using JavaScript. A vulnerable web application might embed untrusted data in the output, without filtering or encoding it. In this way, an attacker can inject a malicious script to the application, and the script will be returned in the response. This will then run on the victim&#x27;s browser.</td><td></td></tr><tr><td>1</td><td>In particular, sanitization of hazardous characters was not performed correctly on user input or untrusted data.</td><td></td></tr><tr><td>1</td><td>In reflected attacks, an attacker tricks an end user into sending request containing malicious code to a vulnerable Web server, which then reflects the attack back to the end user&#x27;s browser.</td><td></td></tr><tr><td>1</td><td>The server receives the malicious data directly from the HTTP request and reflects it back in the HTTP response. The most common method of sending malicious content is adding it as a parameter in a URL that is posted publicly or e-mailed directly to the victim. URLs that contain the malicious script constitute the core of many phishing schemes, whereby the convinced victim visits a URL that refers to a vulnerable site. The site then reflects the malicious content back to the victim, and then the content is</td><td></td></tr></table>

<div align="right">第7页</div>

# WASC Threat Classification

<table><tr><td>Threat</td><td colspan="2">Number of Issues</td></tr><tr><td>Cross-site Scripting</td><td>10</td><td></td></tr><tr><td>Information Leakage</td><td>28</td><td></td></tr><tr><td>Integer Overflows</td><td>3</td><td></td></tr><tr><td>Server Misconfiguration</td><td>1</td><td></td></tr></table>

<div align="right">第8页</div>

# Issues Sorted by Issue Type

# Issue 1 of 2

<table><tr><td colspan="2">Password Parameter in Query</td></tr><tr><td>Severity:</td><td>High</td></tr><tr><td>CVSS Score:</td><td>8.5</td></tr><tr><td>URL:</td><td>http://**************/console/api/datasets</td></tr><tr><td>Entity:</td><td>keyword (Parameter)</td></tr><tr><td>Risk:</td><td>It may be possible to steal sensitive data such as usernames and passwords that are sent in the query string</td></tr><tr><td>Cause:</td><td>SSL (Secure Socket Layer) provides data confidentiality and integrity to HTTP. By encrypting HTTP messages, SSL protects from attackers eavesdropping or altering message contents. Login pages should always employ SSL to protect the user name and password while they are in transit from the client to the server. Lack of SSL use exposes the user credentials as clear text during transmission to the server and thus makes the credentials susceptible to eavesdropping.</td></tr><tr><td>Fix:</td><td>Always use SSL and POST (body) parameters when sending sensitive information.</td></tr></table>

Reasoning: AppScan identified a password parameter that was received in the query string Original Request

GET /console/api/datasets?page=1&limit=30&include_all=true&keyword=1234 HTTP/1.1 Host:************** authorization:Bearer eyJhbGciOiJUzI1NiIsInR6cC161kpXVCJ9. eyJlc2VyX2lkIjoiYmEzZTNlMmUtNGViZS00OTQOLThjYjYtZTc3YmMzM22mMgFKl1iWlZXhwIjoxNzU3MzAyMT U3LCJpc3MiOiJTRUXGX0hPUIRFRCiIsInNlYi161knVbnVbGUGgQVBJIFBhc3Wb3J0In0. THgTbVNXj5yhioNBD7lyoDO67Hxuszh- 6MjU6QYdgI content- type: application/json Accept: \*/ Accept- Language: en- US Referer: http://**************/datasets?category=dataset Cookie: locale=en- US

<div align="right">第9页</div>

# Password Parameter in Query

<table><tr><td>Severity:</td><td>High</td></tr><tr><td>CVSS Score:</td><td>8.5</td></tr><tr><td>URL:</td><td>http://**************/apps</td></tr><tr><td>Entity:</td><td>keywords (Parameter)</td></tr><tr><td>Risk:</td><td>It may be possible to steal sensitive data such as usernames and passwords that are sent in the query string</td></tr><tr><td>Cause:</td><td>SSL (Secure Socket Layer) provides data confidentiality and integrity to HTTP. By encrypting HTTP messages, SSL protects from attackers eavesdropping or altering message contents. Login pages should always employ SSL to protect the user name and password while they are in transit from the client to the server. Lack of SSL use exposes the user credentials as clear text during transmission to the server and thus makes the credentials susceptible to eavesdropping.</td></tr><tr><td>Fix:</td><td>Always use SSL and POST (body) parameters when sending sensitive information.</td></tr></table>

Reasoning: AppScan identified a password parameter that was received in the query string Original Request

GET /apps?keywords=1234%_rsc=uu218 HTTP/1.1 Host:************** Next- Router- State- Tree: 5%22222%2C%7B%22children%22%3A%5B%22(commonLayout)%22%2C%7B%22children%22%3A%5B%22apps%22%2C%7B%22children%22%3A%5B%22P AGE %3F%7B%5C%22keywords%5C%22%3A%5C%221234%5C%22%7D%22%2C%7B%7D%2C%22%Fapps%3Fkeywords%3D1234%22%2C%22refresh%22%5D%7D%2 Cnull%2Cnull%5D%7D%2Cnull%2Cnull%5D%7D%2Cnull%2Cnull%2Ctrue%5D Next- Router- Prefetch: 1 User- Agent: Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36 Next- Url: /apps RSC: 1 Accept: \*/\* Accept- Language: en- US Referrer: http://**************/apps?keywords=1234 Connection: keep- alive Cookie: locale=en- US

<div align="right">第10页</div>

# Issue 1 of 1

<table><tr><td colspan="2">Weak Signature in JWT</td></tr><tr><td>Severity:</td><td>Medium</td></tr><tr><td>CVSS Score:</td><td>7.1</td></tr><tr><td>URL:</td><td>http://**************/console/api/version</td></tr><tr><td>Entity:</td><td>version (Page)</td></tr><tr><td>Risk:</td><td>It might be possible to escalate user privileges and gain administrative permissions over the web application
It may be possible to bypass the web application&#x27;s authentication mechanism</td></tr><tr><td>Cause:</td><td>The application server has no proper verification for the requests with JWT header having &quot;none&quot; algorithm.</td></tr><tr><td>Fix:</td><td>It is necessary to keep a white list of authorised algorithms on the application side and to dismiss all tokens having a signature algorithm that is different from the one authorised on the server</td></tr></table>

Reasoning: The test result seems to indicate a vulnerability because the Test Response is identical to the Original Response, indicating that the Application access is successful with Weak Signature.

<div align="right">第11页</div>

# Issue 1 of 1

<table><tr><td colspan="2">Credit Card Number Pattern Found (American Express) Over Unencrypted Connection</td></tr><tr><td>Severity:</td><td>Low</td></tr><tr><td>CVSS Score:</td><td>5.0</td></tr><tr><td>URL:</td><td>http://**************/_next/static/chunks/4602-64cad2c8d0a207f6.js</td></tr><tr><td>Entity:</td><td>4602-64cad2c8d0a207f6.js (Page)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive information about the web application such as usernames, passwords, machine name and/or sensitive file locations</td></tr><tr><td>Cause:</td><td>Insecure web application programming or configuration</td></tr><tr><td>Fix:</td><td>Remove credit card numbers from your website</td></tr></table>

Reasoning: The response contains a complete American Express credit card number. Raw Test Response:

.187864,732.7429163887613,11989.60988270091,1300.012278487897,335.28490093152146,31.*************,3.***************,47.2 32241542899175,652.7371796173471,5132.414255594984,- 6909.087078780055,- 1001.9990371107289,- 103. ..

# Issue 1 of 1

<div align="right">第12页</div>

<table><tr><td colspan="2">Credit Card Number Pattern Found (Diners Club) Over Unencrypted Connection</td></tr><tr><td>Severity:</td><td>Low</td></tr><tr><td>CVSS Score:</td><td>5.0</td></tr><tr><td>URL:</td><td>http://**************/_next/static/chunks/4602-64cad2c8d0a207f6.js</td></tr><tr><td>Entity:</td><td>4602-64cad2c8d0a207f6.js (Page)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive information about the web application such as usernames, passwords, machine name and/or sensitive file locations</td></tr><tr><td>Cause:</td><td>Insecure web application programming or configuration</td></tr><tr><td>Fix:</td><td>Remove credit card numbers from your website</td></tr></table>

Reasoning: The response contains a complete Diners Club credit card number. Raw Test Response:

.025322027e- 14,13919314567432,00288463683916],[.0541865640643,- 3.**************,- .02911007808948,6.36317777566148, 00848709379851,- 8.54751527471874,- .00851165645469,9.*************,- .00834990904936,- 8.81498681370. .

# Issue 1 of 1

# Credit Card Number Pattern Found (Visa) Over Unencrypted Connection

<table><tr><td>Severity:</td><td>Low</td></tr><tr><td>CVSS Score:</td><td>5.0</td></tr><tr><td>URL:</td><td>http://**************/_next/static/chunks/4602-64cad2c8d0a207f6.js</td></tr><tr><td>Entity:</td><td>4602-64cad2c0d0a207f6.js (Page)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive information about the web application such as usernames, passwords, machine name and/or sensitive file locations</td></tr><tr><td>Cause:</td><td>Insecure web application programming or configuration</td></tr><tr><td>Fix:</td><td>Remove credit card numbers from your website</td></tr></table>

Reasoning: The response contains a complete Visa credit card number.

# Raw Test Response:

623817898e- 27,1475637723558782e- 27,8371015190102975e- 28,2599706096327376e- 28,- 5461314069809755e- 27,- 4921085770524055e- 27,- 4343405037091838e- 27,- 3732668368707687e- 27,- 3093523840190885e- 27,- 2430835727329465e- 27,- 173467. ..

<div align="right">第13页</div>

2,0,0,0,0,2283748241799531e- 28,4037858874020686e- 28,2146547464825323e- 28,5461314069809755e- 27,4921085770524055e- 27,4343405037091838e- 27,3732668368707687e- 27,3093523840190885e- 27,2430835727329466e- 27,17346790100. ..

# Issue 1 of 1

<table><tr><td colspan="2">Missing &quot;Content-Security-Policy&quot; header</td></tr><tr><td>Severity:</td><td>Low</td></tr><tr><td>CVSS Score:</td><td>5.0</td></tr><tr><td>URL:</td><td>http://**************/</td></tr><tr><td>Entity:</td><td>************** (Page)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive information about the web application such as usernames, passwords, machine name and/or sensitive file locations
It is possible to persuade a naive user to supply sensitive information such as username, password, credit card number, social security number etc.</td></tr><tr><td>Cause:</td><td>Insecure web application programming or configuration</td></tr><tr><td>Fix:</td><td>Config your server to use the &quot;Content-Security-Policy&quot; header with secure policies</td></tr></table>

Reasoning: AppScan detected that the Content- Security- Policy response header is missing or with an insecure policy, which increases exposure to various cross- site injection attacks

# Raw Test Response:

User- Agent: Mozilla/5.0 (Windows NT 6.2; W0w64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36 content- type: application/json Accept: $\neq \neq \neq$ Accept- Language: en- US Referrer: http://**************/apps Connection:keep- alive Cookie:locale $=$ en- US

HTTP/1.1 200 OK Server: nginx/1.29.0 Date: Mon, 08 Sep 2025 03:07:49 GMT Content- Type: application/json/json Content- Length: 826 Connection: keep- alive X- Version: 1.6.0 X- Env: PRODUCTION

{ "sso_enforced_for_signin": false, "sso_enforced_for_signin_protocol": "", "enable_marketplace": true, "max_plugin_package_size": 52428800, "enable_email_code_login": false, "enable_email_password_login": true, "enable_social_auth_login": false, "is_allow_create_work": false, "is_allow_create_workspace": false,

<div align="right">第14页</div>

# Issue 1 of 1

# Missing or insecure "X-Content-Type-Options" header

<table><tr><td>Severity:</td><td>Low</td></tr><tr><td>CVSS Score:</td><td>5.0</td></tr><tr><td>URL:</td><td>http://**************/</td></tr><tr><td>Entity:</td><td>************** (Page)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive information about the web application such as usernames, passwords, machine name and/or sensitive file locations
It is possible to persuade a naive user to supply sensitive information such as username, password, credit card number, social security number etc.</td></tr><tr><td>Cause:</td><td>Insecure web application programming or configuration</td></tr><tr><td>Fix:</td><td>Config your server to use the &quot;X-Content-Type-Options&quot; header with &quot;nospiff&quot; value</td></tr></table>

Reasoning: AppScan detected that the "X- Content- Type- Options" response header is missing or has an insecure value, which increases exposure to drive- by download attacks

# Raw Test Response:

User- Agent: Mozilla/5.0 (Windows NT 6.2; W0w64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36 Accept: $\star \int \mathcal{A}$ Refer: http://**************/apps Connection: keep- alive authorization:Bearer eyJhbGciOiJIUzINiIsInRlRrCI6ci6I6kpXVCJ9. eyJ1c2VyX21kIjoiYmEzZTNlYmUtNGViZs00OTQoLThjYjYtZTC3YmMzM2ZmMgFkIiwiZXhwIjoxNzU3MzA0MD AzLCJpc3MiOiJTRUxGxOhPUIRrCI6isInN1YiI61kNvbnNvbGUgQVBJIFBhc3Nwb33DIn0. oow- hGHEdwHqCLKpEqfWdrqLDVaw4wbQdwQhG- oo10

HTTP/1.1 200 OK Server: nginx/1.29.0 Date: Mon, 08 Sep 2025 03:00:15 GMT Content- Type: application/javascript; charset=UTF- 8 Content- Length: 521 Connection: keep- alive Cache- Control: public, max- age=31536000, immutable Accept- Ranges: bytes Last- Modified: Thu, 10 Jul 2025 10:00:58 GMT ETag: W/209- 197f3c7f790 Vary: Accept- Encoding

(self.webpackChunk N_E=self.webpackChunk_N_E||[]).push([7358],{42943:(e,s,n) $\equiv \geq$ {Promise.resolve().then(n.t.bind(n,75715,23)),Promise.resolve().then(n.t.bind(n,34889,23)),Promise.resolve().then(n.t.bind( n,74373,23)),Promise.resolve().then(n.t.bind(n,81474,23)),Promise.resolve().then(n.t.bind(n,72850,23)),Promise.resolve().th en(n.t.bind(n,56234,23)),Pr... $\Xi = \Xi = \Xi$ e.e.s=s);e.O(0,[4309,2453], $() = >$ s(20486),s(42943)),_N_E=e.O()););

<div align="right">第15页</div>

# Issue 1 of 1

<table><tr><td colspan="2">Missing or insecure &quot;X-XSS-Protection&quot; header</td></tr><tr><td>Severity:</td><td>Low</td></tr><tr><td>CVSS Score:</td><td>5.0</td></tr><tr><td>URL:</td><td>http://**************/</td></tr><tr><td>Entity:</td><td>************** (Page)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive information about the web application such as usernames, passwords, machine name and/or sensitive file locations
It is possible to persuade a naive user to supply sensitive information such as username, password, credit card number, social security number etc.</td></tr><tr><td>Cause:</td><td>Insecure web application programming or configuration</td></tr><tr><td>Fix:</td><td>Config your server to use the &quot;X-XSS-Protection&quot; header with value &#x27;1&#x27; (enabled)</td></tr></table>

Reasoning: AppScan detected that the X- XSS- Protection response header is missing or with an insecure value, which may allow Cross- Site Scripting attacks

# Raw Test Response:

User- Agent: Mozilla/5.0 (Windows NT 6.2; w0w64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36 Accept: $\neq \neq \neq$ Accept- Language: en- US Referrer: http://**************/apps Connection:keep- alive authonction:bearer eyJhbGciOiJiUzINiIsInR5cCI6IkpXVCJ9. eyJ1c2VyX21kIjoiYmEzZTNlYmUtNGViZSOOOTQOLThjYJYtZTC3YmMzM2ZmMCFkIiwiZXhWIjoxNzU3MzA0MD AzLCJpc3MiOiJTRUXGX0hPUIRFRCiIsnN1Yi161kNvbnNvbGUgQVBJIFBhc3Mwb3JOIn0. oow- hGHEdwHqCLKpEqfWdrqLDVaw4wb0dw4QhG- oo10

HTTP/1.1 200 OK Server: nginx/1.29.0 Date: Mon, 08 Sep 2025 03:00:15 GMT Content- Type: application/javascript; charset=UTF- 8 Content- Length: 521 Connection:keep- alive Cache- Control: public, max- age $=$ 31536000, immutable Accept- Ranges: bytes Last- Modified: Thu, 10 Jul 2025 10:00:58 GMT ETag:W/209- 197f3c7f790 Vary: Accept- Encoding

(self.webpackChunk_N_B=self.webpackChunk_N_E||[]).push([7358],{42943: $(e,s,n) = >$ {Promise.resolve().then(n.t.bind(n,7515,23)),Promise.resolve().then(n.t.bind(n,34889,23)),Promise.resolve().then(n.t.bind( n,74373,23)).Promise.resolve().then(n.t.bind(n,81474,23)),Promise.resolve().then(n.t.bind(n,72850,23)),Promise.resolve().th en(n.t.bind(n,56234,23)),Pr... $\scriptstyle {\mathrm{s}} = \Xi = >\Xi$ e. $\Xi = \Xi$ )e.O(0,[4309,2453], $() = >$ s(20486),s(42943)),_NE=e.O()|]);

<div align="right">第16页</div>

# Issue 1 of 1

# Sensitive Authentication(Basic) Information Leak

<table><tr><td>Severity:</td><td>Low</td></tr><tr><td>CVSS Score:</td><td>5.0</td></tr><tr><td>URL:</td><td>http://**************/_next/static/chunks/9255-0a642feac1a3cd17.js</td></tr><tr><td>Entity:</td><td>9255-0a642feac1a3cd17.js (Page)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive information about the web application such as usernames, passwords, machine name and/or sensitive file locations</td></tr><tr><td>Cause:</td><td>Insecure web application programming or configuration</td></tr><tr><td>Fix:</td><td>Protect sensitive information and avoid accidental leaks</td></tr></table>

Reasoning: The application has sent a response exposing sensitive information related to Authentication. Raw Test Response:

ction: test.

<div align="right">第17页</div>

# Issue 1 of 3

<table><tr><td colspan="2">Client-Side (JavaScript) Cookie References</td></tr><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/_next/static/chunks/8641-dac4c1939cc0d77c.js</td></tr><tr><td>Entity:</td><td>&quot;use strict&quot;;(self.webpackChunk_N_E=self.webpackChunk_N_E||)).push([[8641],{1716:(e,t,n)=&gt;{function... (Page)</td></tr><tr><td>Risk:</td><td>The worst case scenario for this attack depends on the context and role of the cookies that are created at the client side</td></tr><tr><td>Cause:</td><td>Cookies are created at the client side</td></tr><tr><td>Fix:</td><td>Remove business and security logic from the client side</td></tr></table>

Reasoning: AppScan found a reference to cookies in the JavaScript. Original Response

HTTP/1.1 200 OK Server: nginx/1.29.0 Date: Mon, 08 Sep 2025 03:07:14 GMT Content- Type: application/javascript; charset=UTF- 8 Content- Length: 136551 Connection: keep- alive Cache- Control: public, max- age=31536000, immutable Accept- Ranges: bytes Last- Modified: Thu, 10 Jul 2025 10:00:58 GMT ETag: W/21567- 197f3c7f90" Vary: Accept- Encoding

"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||)).push([[8641],{1716:(e,t,n)=>{function r() {return"undefined"! \(=\) typeof sENTRY_BROWSER(Bundle &&!SEMTRY_BROWSER(Bundle )function i() return"npm"}n.d(t,{Z: \(\scriptstyle \{\} = \{\} ,\{\} ,\{\} ,\{\} ,\{\} ,\{\} ,\{\} ,\{\} ,\{\} ,\{\} ,\{\} ,\{\} ,\{\} ,\{\} ,\{\} ,\{\} ,\{\} ,\{\} ,\{\} ,\{\} ,\{\} ,\{\} ,\{\} ,\{\} ,\{\} ,\{\} ,\) t=0,i.zf(),n={sid:(0,s.eJ)),init:!,timestamp:t,started:t,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON: \(() = >\) {var e3=return e=n,(0. t.e2s({s(e. side):e. status,errors:etered.new date("le3\*e.start=toIS0string),timestamp:new Date(e1e3\*e. timestamp).pid(Sid: \(\) did):e. status: e. start:e. errors,did: did: number==typeof e.did|'"string"==typeof e.did? \\(e.did):void 0,duration:abnormal_mechanism:e.abnormal_mechanism,attrs: {release:e.release,environment:e.environment,ip_address:e.ipAddress,user_agent:e.userAgent)))};return &&o(n,e),n}function o(e,t=|)if(t.user||(e.ipAddress&&t.user.ip_address&&(e.ipAddress= t. user.ip address),e.did|t. did| (e. did = t. did.length: t. did: 0,s,e)(),void 0 = t. init:t. init:t. init:,e.did= t. did: e. did = \\(t. did)},"number" = typeof t. started & (e. started = t. started),e. ignoreDuration: e. duration = void 0;else if("number" = =typeof t. duration = t. duration;else{let t = e. timestamp = e. started; e. duration = t = 0?t:0} t.release & (e.release = t. release),t.environment & (e.environment = t.environment),!e...

<div align="right">第18页</div>

<table><tr><td colspan="2">Client-Side (JavaScript) Cookie References</td></tr><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/_next/static/chunks/app/signin/page-b6a16b76969204c7.js</td></tr><tr><td>Entity:</td><td>(self.webpackChunk_N_E=self.webpackChunk_N_E||).push([[4217],{2226:(e,t,a)=&gt;{&quot;use strict&quot;;a.d(t,{R... (Page)</td></tr><tr><td>Risk:</td><td>The worst case scenario for this attack depends on the context and role of the cookies that are created at the client side</td></tr><tr><td>Cause:</td><td>Cookies are created at the client side</td></tr><tr><td>Fix:</td><td>Remove business and security logic from the client side</td></tr></table>

Reasoning: AppScan found a reference to cookies in the JavaScript. Original Response

\(\dots = = = 0\) i0. SAML?z(o).then \(\mathbb{C} = =\) {a.push(e.url)).finally \(() = >\{\) d(1) \(\}\) \(\scriptstyle \mathbf{t} = = = = 0\) i0. OIDc?M(o).then \(\mathbb{C} = =\) (document.cookie \(=\) user- oidcstate \(= \frac{5}{5}\) {e.state);Path \(= \int\) ,a.push(e.url)).finally \(() = >\{\) d(1) \(\}\) \(\scriptstyle \mathbf{t} = = = = 0\) i0. OAuth2\\(o).then\)\mathbb{C}=\(document.cookie\)=\("user - oauth2 - state\)=\frac{5}{5}\(e.state);Path\)=\int\(,a.push(e.url)).finally\)\mathbb{C}=\(d(1) \)\}\(f...

# Issue 3 of 3

<table><tr><td colspan="2">Client-Side (JavaScript) Cookie References</td></tr><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/_next/static/chunks/4558-da6c1d12157fba00.js</td></tr><tr><td>Entity:</td><td>(self.webpackChunk_N_E=self.webpackChunk_N_E||).push([[4558],{261:(t,e,n)=&gt;{&quot;use strict&quot;;n.d(e,{A... (Page)</td></tr><tr><td>Risk:</td><td>The worst case scenario for this attack depends on the context and role of the cookies that are created at the client side</td></tr><tr><td>Cause:</td><td>Cookies are created at the client side</td></tr><tr><td>Fix:</td><td>Remove business and security logic from the client side</td></tr></table>

Reasoning: AppScan found a reference to cookies in the JavaScript. Original Response

HTTP/1.1 200 OK Server: nginx/1.29.0 Date: Mon, 08 Sep 2025 03:07:17 GMT Content- Type: application/javascript; charset=UTF- 8 Content- Length: 1685765 Connection: keep- alive Cache- Control: public, max- age=31536000, immutable Accept- Ranges: bytes Last- Modified: Thu, 10 Jul 2025 10:00:58 GMT ETag: W/"19b905- 197f3c7E790" Vary: Accept- Encoding

(self.webpackChunk_N_E=Self.webpackChunk_N_E||).push([[4558],{261:(t,e,n)=>{"use strict";n.d(e,{A:=>v);var r=n(47625),a=n(89536),l=n(64595),c=n(66064),p=a.A?a.A.isConcatSpreadable:void 0;let d=function(t){return(0,c.A)(t)l(0,1. A) (t)l!p&&t&t[p]);v=function t(e,n,a,l,c)(var p=- 1,v=e.length;for(a||a=d),c||(c=|)+p<v);var x=[p];n>0&&a(x)?n>1? t(x,n- 1,a,l,c):(0,r.A)(c,x)l|l(c(.length)=x)return c)),351:(t,e,n)=>{"use strict";var r=object.assign|function(t) {for(var e=l;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&

<div align="right">第19页</div>

t[r=n[r])return t),a=function(){function t(t,e){for(var n=d,n<e.length;n++){var r=e[n];r enumerable=r.eenumerable||1,r.configurable=10,"value=in r&& (r.writable $\coloneqq !0$ ),objectdefineProperty(t,r.key,r))return function(e,n,r){return n&&(e.prototype,n),r&&(e,r),e} (), $\mathbb{1} = \mathbb{n}$ (4890), $\epsilon = \epsilon \mathbb{d}$ 1), $\mathbb{P} = \mathbb{D}$ (n(89433)){function d(t){return t&t.__esModule?t:(default:t)}var $\mathbb{V} =$ {position:"absolute",top:0,left:0,visibility:"hidden",height:0,overflow:"scroll",whiteSpace:"pre"),x= ["extraWidth","injectStyles","inputClassName","inputRef","inputStyle","minWidth","onAutosize","placeholderIsMinWidth"],_=fu nction(t,e) e.style.fontSize=t.fontSize,e.style.fontFamily $= \pm$ .fontFamily,e.style.fontSize $= \pm$ .fontSize,e.style.fontStyle $= \pm$ .fontSize, e.style.fontSize=\t盛世Spacin...window&&!windowNAVigator&&/MSIE |Trident//Edge//.test(window NAVigator. userAgent),wfunction(){return b?"_"+Math.random().toString(36).substr(2,12):void 0), $\Xi =$ function(t){function e(t)!(function(t,e){if(!t...

# Issue 1 of 6

# Email Address Pattern Found

<table><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/_next/static/chunks/6133-2a596c0b72baec18.js</td></tr><tr><td>Entity:</td><td>6133-2a596c0b72baec18.js (Page)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive information about the web application such as usernames, passwords, machine name and/or sensitive file locations</td></tr><tr><td>Cause:</td><td>Insecure web application programming or configuration</td></tr><tr><td>Fix:</td><td>Remove e-mail addresses from the website</td></tr></table>

Reasoning: The response contains an e- mail address that may be private.

# Raw Test Response:

.a+=?subject $= \frac{0}{0}$ {encodeurIComponent(t)},s&&(a+=body=\ $encodeurIComponent(s)},a),r$ $e,t,s)=\geq a("<EMAIL>","Technical Support Request$ \xi\{\texttt{t}\}\(\)\xi\{\texttt{e}\}\$ Please do not remove the following information:

# Issue 2 of 6

<div align="right">第20页</div>

# Email Address Pattern Found

<table><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/_next/static/chunks/app/(commonLayout)/layout-555df4ce5c8df404.js</td></tr><tr><td>Entity:</td><td>layout-555df4ce5c8df404.js (Page)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive information about the web application such as usernames, passwords, machine name and/or sensitive file locations</td></tr><tr><td>Cause:</td><td>Insecure web application programming or configuration</td></tr><tr><td>Fix:</td><td>Remove e-mail addresses from the website</td></tr></table>

Reasoning: The response contains an e- mail address that may be private.

# Raw Test Response:

...&(r+=?subject=\({encodeURIComponent(t)})',s&&(r+=&body=\){encodeURIComponent(s)})',r),a=e,t,s)=>r("<EMAIL>",Technical Support Request}{t}\({e}Please do not remove the following information:

# Issue 3 of 6

# Email Address Pattern Found

<table><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/_next/static/chunks/8717-023615e5fc4f49ab.js</td></tr><tr><td>Entity:</td><td>8717-023615e5fc4f49ab.js (Page)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive information about the web application such as usernames, passwords, machine name and/or sensitive file locations</td></tr><tr><td>Cause:</td><td>Insecure web application programming or configuration</td></tr><tr><td>Fix:</td><td>Remove e-mail addresses from the website</td></tr></table>

Reasoning: The response contains an e- mail address that may be private. Raw Test Response:

...rom 19:00 to 24:00 UTC on August 28 for an upgrade. For questions, kindly contact our support team (<EMAIL>). We value your patience."za_Hans:"为了有效提升数据检索能力及稳定性，Dify将于2023年8月29日03:00至08:00期间进行服务升级，届时Dify云端版及应用将无法访问。感谢您的耐心与支持。",pt_BR:"Our system will be unavailable from 19:00 to 24:00 UTC on August 28 for an upgrade. For questions, kindly contact our support team (<EMAIL>). We value your patience.",es_ES:"Our system will be unavailable from 19:00 to 24:00 UTC on August 28 for an upgrade. For questions, kindly contact our support team (<EMAIL>). We value your patience.",fr_FR:"Our system will be unavailable from 19:00 to 24:00 UTC on August 28 for an upgrade. For questions, kindly contact our support team (<EMAIL>). We value your patience.",de_DE:"Our system will be unavailable from 19:00 to 24:00 UTC on August 28 for an upgrade. For questions, kindly contact our support team (<EMAIL>). We value your patience.",ja_JP:"Our system will be unavailable from 19:00 to 24:00 UTC on August 28 for an upgrade. For questions, kindly contact our support team (<EMAIL>). We value your patience.",ko_KR:"시스템이 업그레이드를 위해 UTC 시간로 8월 28일 19:00 \~ 24:00 에 사용 불가될 예정입니다. 질문이 있으시면 지원 팀에 연락주세요 (<EMAIL>). 최선을 다해 답변해드리겠습니다.",pl_PL:"Nasz system bedzie niedostepny od 19:00 do 24:00 UTC 28 sierpnia w celu aktualizacji. W

<div align="right">第21页</div>

przypadku pytafor prosimy o kontakt z naszym zespolem wsparcia (<EMAIL>). Doceniamy Twoja ciezpliwo56. ",uk UA:"Hamae cucrema 6yne hepctynha. s 19:00 no 24:00 UTC 28 cernhs aria oHbJHra. dKyo y bac buhkhnyts sanutahn, Gyns Jacka, SB'xmitbcra s haooc cnyxo nIaprmk (<EMAIL>). Jakyemo sa tepnHnra."ru RU:"Haua csctema cnet hepctynha c 19:00 no 24:00 UTC 28 abrycra nia oHbJHra. No bonpcam, nokanyoTa, oSpamaitecs b Hauy cnyx6y nolnepxmu (<EMAIL>). Cnacuo sa bame tepneHne",vi_VN:"Hg thong cua ch\xfang t\xf4i se ngung hoat dng t0 19:00 den 24:00 UTC v\xe0o ng\xe0y 28 th\xeIng 8 de n\xe2ng cap. N8u c\xf3 thac mac, vui 1\xf2ng li\xean he voi nh\xf3m h5 trg cua ch\xfang t\xf4i (<EMAIL>). Ch\xfang t\xf4i d\xelnh gi\xel cao su ki\xean nan cua bgn."tr_TR:"Sistemiz, 28 Agustos'ta 19:00 ile 24:00 UTC saatleri arastanda dxfacelleme nedeniyile kullanlanaayacaktr. Sorulariniz i\xe7in 1\xfctifen destek ekibimizle iletisime ge\xe7in (<EMAIL>). Sabriniz i\xe7in tesekh(xfr ederiz.",fa IR:"24:00 Li19:00 cL jL puxUTC jL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL J L JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL IL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JE JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL HL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JC JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JR JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL RL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL ML JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL L JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL L JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JLL JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL L JL E JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL

# Issue 4 of 6

# Email Address Pattern Found

<table><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/_next/static/chunks/app/signin/page-b6a16b76969204c7.js</td></tr><tr><td>Entity:</td><td>page-b6a16b76969204c7.js (Page)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive information about the web application such as usernames, passwords, machine name and/or sensitive file locations</td></tr><tr><td>Cause:</td><td>Insecure web application programming or configuration</td></tr><tr><td>Fix:</td><td>Remove e-mail addresses from the website</td></tr></table>

Reasoning: The response contains an e- mail address that may be private. Raw Test Response:

cursor- pointer text- xs font- medium text- text- accent- secondary",children:(0,s.jsx)"a",(href:"mailto:request- <EMAIL>",children:"request- <EMAIL>")))))),needsDelay:!,0,children:(0,s.jsx)"span", {className:"cursor- pointer text- text- accent- seconda...

rom 19:00 to 24:00 UTC on August 28 for an upgrade. For questions, kindly contact our support team (<EMAIL>). We value your patience.",zh_ims:"为了有效提升数据检索能力及稳定性, D3f.将于 2023 年 8 月 29 日 03:00 至 08:00 期间进行服务升级, 届时 Dify 云端版及应用将无法访问。感谢您的耐心与支持。",pt_BR:"Our system will be unavailable from 19:00 to 24:00 UTC on August 28 for an upgrade. For questions, kindly contact our support team (<EMAIL>). We value your patience.",es_ES:"Our system will be unavailable from 19:00 to 24:00 UTC on August 28 for an upgrade. For questions, kindly contact our support team (<EMAIL>). We value your patience.",fr_FR:"Our system will be unavailable from 19:00 to 24:00 UTC on August 28 for an upgrade. For questions, kindly contact our support team (<EMAIL>). We value your patience.",ko_KR:"시스템이 업그레 이드를 위해 UTC 시간대로 8월 28 일 19:00 \~ 24:00 에 사용 불가될 예정입니다. 질문이 있으시면 지원 팀에 연락주세요 (<EMAIL>). 최선 을 다해 답변해드리겠습니다. ",pl_PL:"Nasz system bedzie niedostępny od 19:00 do 24:00 UTC 28 sieprnia w celu aktualizacji. W przypadku pytań prosimy o kontakt z naszym zespolem wsparcia (<EMAIL>). Doceniamy Twoja cieprliwosć.",uk UA:"Hama csetema Gynie hepctynha s 19:00 po 24:00 UTC 28 cepnha) для омгловеня. Якция у вас винкннуть сапитання, буль ласка, зв'жяться зя замоо службю пигтримки (<EMAIL>). Дякузе за терпнно.",ru RU:"Haua sистema будет недоступна с 19:00 no 24:00 UTC 28 abrycra для oбновления. No вопросam, nokanyoTa, oSpamaitecs b hauy cnyx6y nolnepxku (<EMAIL>). Cnacuo sa bame tepneHne",vi_VN:"Hg thong cua ch\xfang t\xf4i se ngung hoat dng tu 19:00 den 24:00 UTC v\xe0o ng\xe0y 28 th\xeng 8 de n\xe2ng cap. N8u c\xf3 thac mac, vui 1\xf2ng li\xean he voi nh\xf3m h5 trg cua ch\xfang t\xf4i (<EMAIL>). Ch\xfang t\xf4i d\xelnh gi\xel cao su ki\xean nan cua bgn.",tr_TR:"Sistemiz, 28 Agustos'ta 19:00 ile 24:00 UTC saatleri arastanda g\xfncelleme nedeniyle kullanlamaayacaktr. Sorulariniz i\xe7in 1\xfctifen destek ekibimizle iletisime ge\xe7in (<EMAIL>). Sabriniz i\xe7in tesekh(xfr cserisu,"fa IR:"24:00 Li19:00 jsL jL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL L

przypadku pytafor prosimy o kontakt z naszym zespolem wsparcia (<EMAIL>). Doceniamy Twoja cieprliwosć.",uk UA:"Hama csetema Gynie hepctynha s 19:00 po 24:00 UTC 28 cepnha) для oмгловеня. Якция у вас винкннуть сапитання, буль ласка, зв'жяться зя замоо службю пигтримки (<EMAIL>). Дякузе за терпнно.",ru RU:"Haua sистema行政审批 nenzhe jactyna c 19:00 no 24:00 UTC 28 abrycra для oбновления. No вопросam, nokanyoTa, oSpamaitecs b hauy cnyx6y nolnepxku (<EMAIL>). Cnacuo sa bame tepneHne",vi_VN:"Hg thong cua ch\xfang t\xf4i se ngung hoot dng tu 19:00 den 24:00 UTC v\xe0o ng\xe0y 28 th\xeng 8 de n\xe2ng cap. N8u c\xf3 thac mac, vui 1\xf2ng li\xean he voi nh\xf3m h5 trg cua ch\xfang t\xf4i (<EMAIL>). Ch\xfang t\xfg4i d\xelnh gi\xel cao su ki\xean nan cua bgn.",tr_TR:"Sistemiz, 28 Agustos'ta 19:00 ile 24:00 UTC saatleri arastanda g\xfncelleme nedeniyle kullanlamaayacaktr. Sorulariniz i\xe7in 1\xfctifen destek ekibimizle iletistisime ge\xe7in (<EMAIL>). Sabriniz i\xe7in tesekh(xfr cserisu,"fa IR:"24:00 Li19:00 jsL jL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL JL L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L LL L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L L LL

<div align="right">第22页</div>

nao skupino za podporo (<EMAIL>).Cenimo vaao potree211ivost.,"th TH: 19:00 24:00 UTC 28 24:00 UTC (e,t,a) $= >$ {Promise.resolve().then(a.bind(a,21521))}.

# Issue 5 of 6

# Email Address Pattern Found

<table><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/console/api/account/profile</td></tr><tr><td>Entity:</td><td>profile (Page)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive information about the web application such as usernames, passwords, machine name and/or sensitive file locations</td></tr><tr><td>Cause:</td><td>Insecure web application programming or configuration</td></tr><tr><td>Fix:</td><td>Remove e-mail addresses from the website</td></tr></table>

Reasoning: The response contains an e- mail address that may be private.

Raw Test Response:

X- Version: 1.6.0  X- Env: PRODUCTION  Vary: Cookie

id: "ba3e3ebe- 4ebe- 4944- 8cb6- e77bc3ff0ad",  name": "admin",  avatar": null,  avatar_url": null,  email": <EMAIL>",  is_password_set": true,  interface_language": "zh- Hans",  interface_theme": "light",  timezone": "America/New_York",  last_login_at": **********,  last_login_ip": "1************",  created_at": **********

# Issue 6 of 6

<div align="right">第23页</div>

<table><tr><td colspan="2">Email Address Pattern Found</td></tr><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/_next/static/chunks/9255-0a642feac1a3cd17.js</td></tr><tr><td>Entity:</td><td>9255-0a642feac1a3cd17.js (Page)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive information about the web application such as usernames, passwords, machine name and/or sensitive file locations</td></tr><tr><td>Cause:</td><td>Insecure web application programming or configuration</td></tr><tr><td>Fix:</td><td>Remove e-mail addresses from the website</td></tr></table>

Reasoning: The response contains an e- mail address that may be private. Raw Test Response:

...rom 19:00 to 24:00 UTC on August 28 for an upgrade. For questions, kindly contact our support team (<EMAIL>). We value your patience."zah Hans:"为了有效提升数据检索能力及稳定性，Dify将于2023年8月29日03:00至08:00期间进行服务升级，届时Dify云端版及应用将无法访问。感谢您的耐心与支持。",pt_BR:"Our system will be unavailable from 19:00 to 24:00 UTC on August 28 for an upgrade. For questions, kindly contact our support team (<EMAIL>). We value your patience.",es_ES:"Our system will be unavailable from 19:00 to 24:00 UTC on August 28 for an upgrade. For questions, kindly contact our support team (<EMAIL>). We value your patience." ja_JP:"Our system will be unavailable from 19:00 to 24:00 UTC on August 28 for an upgrade. For questions, kindly contact our support team (<EMAIL>). We value your patience.",ko_KR:"시스템이 업그레이드를 위해 UTC 시간대로 8월 28일 19:00 \~ 24:00에 사용 불가될 예정입니다. 질문이 있으시면 지원 팀에 연락주세요 (<EMAIL>). 최선을 다해 답변해드리겠습니다.",pFL:"Nasz system bedzie niedostepny on 19:00 do 24:00 UTC 28 sierpnia w ceni aktualizacji. Wprzypadku pytaf prosimy o kontakt z naszym zespolem wsparcia (<EMAIL>). Doceniamy Twoja cierpliwo6c.",uk UA:"Наша система буде недоступная s 19:00 до 24:00 UTC 28 серния для оновления. Якцо у вас виникнуть заплатания, буль ласка, зв"житься з нашоо службю плятрмки (<EMAIL>). Дякуемо за терпння.",ru_RU:"Наша система будет недоступна с 19:00 до 24:00 UTC 28 августа для обновления. По вопросам, пожалуйста, обращайтесь в нашу службу поллержии (<EMAIL>). Спасибо за ваше терпение",vi_VN:"Не тонд сия сн\xf4i", se ngung hoạt động từ 19:00 đến 24:00 UTC v\xe00 ng\xe0y 28 th\xeing 8 dê n\xe2ng cap. Nếu c\xf3 thác mắc, vui l\xf2ng li\xean hệ vói nh\xf3m hô trg của ch\xfang t\xf4i (<EMAIL>). Ch\xfang t\xf4i d\xelnh gi\xel cao su ki\xean nhắn của bạn.",tr_TR:"Sistemimiz, 28 Agustos'ta 19:00 ile 24:00 UTC saatleri arasanda g\xfncelleme nedeniyle kullanimaayacaktr. Sorulariniz i\xe7in l\xfcfen destek ekibimize iletisime ge\xe7in (<EMAIL>). Sabrinedi i\xe7in tesekl\xfacderiz.",fa IR:"24:00 Li 19:00 dis J . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . ... . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . ,sl . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .  . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .   . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .    . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .       . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .     . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .        . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .      . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

<div align="right">第24页</div>

<table><tr><td colspan="2">Integer Overflow</td></tr><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/console/api/workspaces/current/plugin/tasks</td></tr><tr><td>Entity:</td><td>page_size (Parameter)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive debugging information</td></tr><tr><td>Cause:</td><td>An Integer Overflow (or wraparound) occurs when a value that is too large is stored (larger than the maximum value the variable can hold) in an integer data type (including byte, short, long, and other types). The most significant bits of the integer are lost, and the remaining value is relative to the minimum value (either 0 or very negative value for signed types).</td></tr><tr><td>Fix:</td><td>Verify that parameter values are in their expected ranges and types. Do not output debugging error messages and exceptions</td></tr></table>

Reasoning: The application has responded with an error message, indicating an undefined state that may expose sensitive information.

# Raw Test Response:

User- Agent: Mozilla/5.0 (Windows NT 6.2; W0w64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36 content- type: application/json Accept: $\star \neq \pi$ Accept- Language: en- US Referrer: http://**************/apps Connection: keep- alive Cookie: locale=en- US

HTTP/1.1 500 INTERNAL SERVER ERROR Server: nginx/1.29.0 Date: Mon, 08 Sep 2025 03:04:51 GMT Content- Type: application/json Content- Length: 56 Connection: keep- alive X- Version: 1.6.0 X- Env: PRODUCTION Vary: Cookie

# Issue 2 of 3

<table><tr><td colspan="2">Integer Overflow</td></tr><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/console/api/workspaces/current/plugin/tasks</td></tr><tr><td>Entity:</td><td>page (Parameter)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive debugging information</td></tr><tr><td>Cause:</td><td>An Integer Overflow (or wraparound) occurs when a value that is too large is stored (larger than the maximum value the variable can hold) in an integer data type (including byte, short, long, and other types). The most significant bits of the integer are lost, and the remaining value is relative to the minimum value ( either 0 or very negative value for signed types).</td></tr><tr><td>Fix:</td><td>Verify that parameter values are in their expected ranges and types. Do not output debugging error messages and exceptions</td></tr></table>

<div align="right">第25页</div>

Reasoning: The application has responded with an error message, indicating an undefined state that may expose sensitive information.

# Raw Test Response:

User- Agent: Mozilla/5.0 (Windows NT 6.2; w0w64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36 content- type: application/json Accept: $\pi_{\lambda}$ Accept- Language: en- US Referer: http://**************/apps Connection: keep- alive Cookie: locale=en- US

HTTP/1.1 500 INTERNAL SERVER ERROR Server: nginx/1.29.0 Date: Mon, 08 Sep 2025 03:05:46 GMT Content- Type: application/json Content- Length: 56 Connection: keep- alive X- Version: 1.6.0 X- Env: PRODUCTION Vary: Cookie

# Issue 3 of 3

# Integer Overflow

<table><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/console/api/datasets</td></tr><tr><td>Entity:</td><td>page (Parameter)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive debugging information</td></tr><tr><td>Cause:</td><td>An Integer Overflow (or wraparound) occurs when a value that is too large is stored (larger than the maximum value the variable can hold) in an integer data type (including byte, short, long, and other types). The most significant bits of the integer are lost, and the remaining value is relative to the minimum value (either 0 or very negative value for signed types).</td></tr><tr><td>Fix:</td><td>Verify that parameter values are in their expected ranges and types. Do not output debugging error messages and exceptions</td></tr></table>

Reasoning: The application has responded with an error message, indicating an undefined state that may expose sensitive information.

Raw Test Response:

User- Agent: Mozilla/5.0 (Windows NT 6.2; w0w64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36 content- type: application/json Accept: $\pi_{\lambda}$ Accept- Language: en- US Referer: http://**************/datasets Connection: keep- alive Cookie: locale=en- US

HTTP/1.1 500 INTERNAL SERVER ERROR Server: nginx/1.29.0 Date: Mon, 08 Sep 2025 03:05:46 GMT

<div align="right">第26页</div>

Content- Type: application/json Content- Length: 56 Connection: keep- alive X- Version: 1.6.0 X- Env: PRODUCTION Vary: Cookie

# Issue 1 of 6

<table><tr><td colspan="2">Internal IP Disclosure Pattern Found</td></tr><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/console/api/account/profile</td></tr><tr><td>Entity:</td><td>profile (Page)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive information about the web application such as usernames, passwords, machine name and/or sensitive file locations</td></tr><tr><td>Cause:</td><td>Insecure web application programming or configuration</td></tr><tr><td>Fix:</td><td>Remove internal IP addresses from your website</td></tr></table>

Reasoning: AppScan discovered what looks like an internal IP address in the response.

# Raw Test Response:

"name: "admin", "avatar": null, "avatar_url": null, "email": "<EMAIL>" "is_password_set": true, "interface_language": "zh- hans", "interface_theme": "light", "timezone": "America/New_York", "last_login_at": **********, "last_login_in": "1************", "created_at": **********}

<div align="right">第27页</div>

<table><tr><td colspan="2">Internal IP Disclosure Pattern Found</td></tr><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/console/api/apps</td></tr><tr><td>Entity:</td><td>apps (Page)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive information about the web application such as usernames, passwords, machine name and/or sensitive file locations</td></tr><tr><td>Cause:</td><td>Insecure web application programming or configuration</td></tr><tr><td>Fix:</td><td>Remove internal IP addresses from your website</td></tr></table>

Reasoning: AppScan discovered what looks like an internal IP address in the response. Raw Test Response:

"access mode": null, "create_user_name": null, "author_name": "admin" } "id": "11692054- eeb5b- 452a- 8cc9- 1c69a7a9e452", "name": "\u50c0\u667a\u6587\u6863", "max_active_requests": null, "description": "\u57fa\u4e8e\u5c0f\u667a\u6587\u68630.2.0\u521b\u5efa\u7684\u5d5c\u4f5c\u6d41\uffc0\u53ef\u5728\u5767\u4ed6\u7f16\u6392\u6 d41\u7a0b\u4e2d\u76f4\u6392\u8c03\u7528\u3002\nkb_name\u53ef\u5738http://*************:9999\u83b7\u53ef", "mode": "workflow", "icon_type": "emoji", "icon": "\ud83e\uddl5", "icon_background": "#FFEA5", "icon_url": null, "model_config": null, "workflow": { "id": "97af11b6- 176e- 4a62- a29a- 578672ad6506", "created_by": "ba3e3ebe- 4ebe- 4944- 8cb6- e77bc33ff0ad",

# Issue 3 of 6

<table><tr><td colspan="2">Internal IP Disclosure Pattern Found</td></tr><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/console/api/workspaces/current/model-providers</td></tr><tr><td>Entity:</td><td>model-providers (Page)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive information about the web application such as usernames, passwords, machine name and/or sensitive file locations</td></tr><tr><td>Cause:</td><td>Insecure web application programming or configuration</td></tr><tr><td>Fix:</td><td>Remove internal IP addresses from your website</td></tr></table>

Reasoning: AppScan discovered what looks like an internal IP address in the response.

<div align="right">第28页</div>

# Raw Test Response:

"en_US": "Server url" }, "type": "secret- input", "required": true, "default": null, "options": [], "placeholder": { "zh_Hans": "\u5728\ub664\u8f93\u5165xInference\u7684\u7670a\u52a1\u5668\u5730\u5740\u5740\u5982 http://*************:9997", "en_US": "Enter the url of your XInference, e.g. http://*************:9997", "max_length": 0, "show_on": [] }, { "variable": "model_uid", "label": {

# Issue 4 of 6

# Internal IP Disclosure Pattern Found

<table><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/console/api/datasets</td></tr><tr><td>Entity:</td><td>datasets (Page)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive information about the web application such as usernames, passwords, machine name and/or sensitive file locations</td></tr><tr><td>Cause:</td><td>Insecure web application programming or configuration</td></tr><tr><td>Fix:</td><td>Remove internal IP addresses from your website</td></tr></table>

Reasoning: AppScan discovered what looks like an internal IP address in the response. Raw Test Response:

} "tags": [ 1, "doc_form": null, "external_knowledge_info": { "external_knowledge_id": "8fcfce2c32ea11f09a690242ac120006", "external_knowledge_api_id": "4fa30bdd- 4806- 4998- 9cee- 439c71b53391", "external_knowledge_api_name": "ragflow", "external_knowledge_apiEndpoint": "http://*************/api/v1/dify" }, "external_retrieval_model": { "top_k": 2, "score_threshold": 0.5, "score_threshold_enabled": false },

<div align="right">第29页</div>

"doc_metadata":[  ],  ...  ...

"tags":[

],

"doc_form": null,

"external_knowledge_info": {

"external_knowledge_api_id": "717b38b2- c4e2- 442f- 9870- 9ad9cf3fd983", "external_knowledge_api_name": "\u5c0f\u667a\u6587\u6863", "external_knowledge_apiEndpoint": "http://*************:9999/api/v0/dify"

},

"external_unicisual_model": {

"top_k": 3, "score_threshold": 0.0, "score_threshold_enabled": true

},

"doc_metadata":[

],

# Issue 5 of 6

# Internal IP Disclosure Pattern Found

<table><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/console/api/datasets/external-knowledge-api</td></tr><tr><td>Entity:</td><td>external-knowledge-api (Page)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive information about the web application such as usernames, passwords, machine name and/or sensitive file locations</td></tr><tr><td>Cause:</td><td>Insecure web application programming or configuration</td></tr><tr><td>Fix:</td><td>Remove internal IP addresses from your website</td></tr></table>

Reasoning: AppScan discovered what looks like an internal IP address in the response. Raw Test Response:

{ "data": [ { "id": "4fa30bdd- 4806- 4998- 9cee- 439c71b53391", "tenant_id": "863e438f- d63f- 4fda- 9e51- 64c8566e914b", "name": "ragflow", "description": "", "settings": { "endpoint": "http://*************/api/v1/dify", "api_key": "ragflow- zkDzDg3zjh1MzVm2DExZjBhNzQ5MDI0Mm" }, "dataset bindings": [ { "id": "890ee953- c4f4- 4b5b- b5b6- 99f0dd18bad3", "name": "\u5e74u62a5"

<div align="right">第30页</div>

} "created_by": "ba3e3ebe- 4ebe- 4944- 8cb6- e77bc33ff0ad", "created_at": "2025- 05- 27T09:18:51" } "id": "717b38b2- c4e2- 442f- 9870- 9ad9cf3fd983", "tenant_id": "863e438f- u63f- 4fda- 9e51- 64c8566e914b", "name": "\u50f\u667a\u687\u6863", "description": "", "settings": { "endpoint": "http://*************:9999/api/v0/dify", "api_key": "eyJ:android:5013012121212121212121212121212121212121212121212121212121212121212121212121212121212121212121212121212121 1, "dataset bindings": { "id": "3f14e8ef- b0d1- 4c81- 88ba- 9f5b19afc16a", "name": "doris\u4e2d\u6587\u624b\u518c" } "created_by": "ba3e3ebe- 4ebe- 4944- 8cb6- e77bc33ff0ad",

# Issue 6 of 6

# Internal IP Disclosure Pattern Found

<table><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/console/api/datasets/api-base-info</td></tr><tr><td>Entity:</td><td>api-base-info (Page)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive information about the web application such as usernames, passwords, machine name and/or sensitive file locations</td></tr><tr><td>Cause:</td><td>Insecure web application programming or configuration</td></tr><tr><td>Fix:</td><td>Remove internal IP addresses from your website</td></tr></table>

Reasoning: AppScan discovered what looks like an internal IP address in the response. Raw Test Response:

Date: Mon, 08 Sep 2025 03:02:53 GMT  Content- Type: application/json  Content- Length: 45  Connection: keep- alive  X- Version: 1.6.0  X- Env: PRODUCTION  Vary: Cookie  {  "api_base_url": "http://**************/v1"  }

<div align="right">第31页</div>

# Issue 1 of 1

<table><tr><td colspan="2">Missing &quot;Referrer policy&quot; Security Header</td></tr><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/</td></tr><tr><td>Entity:</td><td>************** (Page)</td></tr><tr><td>Risk:</td><td>It is possible to gather sensitive information about the web application such as usernames, passwords, machine name and/or sensitive file locations
It is possible to persuade a naive user to supply sensitive information such as username, password, credit card number, social security number etc.</td></tr><tr><td>Cause:</td><td>Insecure web application programming or configuration</td></tr><tr><td>Fix:</td><td>Config your server to use the &quot;Referrer Policy&quot; header with secure policies</td></tr></table>

Reasoning: AppScan detected that the Referrer Policy Response header is missing or with an insecure policy, which increases exposure to various cross- site injection attacks

# Raw Test Response:

HTTP/1.1 200 OK Server: nginx/1.29.0 Date: Mon, 08 Sep 2025 02:59:26 GMT Content- Type: application/json Content- Length: 826 Connection: keep- alive X- Version: 1.6.0 X- Env: PRODUCTION

{ "sso_enforced_for_signin": false, "sso_enforced_for_signin_protocol: "", "enable_marketplace": true, "max_plugin_package_size": 52428800, "enable_email_code_login": false, "enable_email_passwordlogin": true, "enable_social_auth_login": false, "is_allow_register": false, "is_allow_create_workspace": false, "is_email_setup": true,

<div align="right">第32页</div>

# Issue 1 of 3

# Possible Server Path Disclosure Pattern Found

<table><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/_next/static/chunks/8058-427e825e255e5dd.js</td></tr><tr><td>Entity:</td><td>8058-427e825bd255e5dd.js (Page)</td></tr><tr><td>Risk:</td><td>It is possible to retrieve the absolute path of the web server installation, which might help an attacker to develop further attacks and to gain information about the file system structure of the web application.</td></tr><tr><td>Cause:</td><td>Latest patches or hotfixes for 3rd. party products were not installed</td></tr><tr><td>Fix:</td><td>Download the relevant security patch for your web server or web application.</td></tr></table>

Reasoning: The response contains the absolute paths and/or filenames of files on the server.

# Raw Test Response:

$\ldots = = = \mathbb{n}$ && $\mathbb{G} = \mathbb{I}$ $\scriptstyle{\mathbb{t} = "0}$ $\scriptstyle \Pi = \Pi = \Pi$ );var $\mathtt{y} = "\mathtt{S}" = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =}$ ?1/[\\$p].test(T)? d: $\scriptstyle \Pi = \Pi$ [T], $\mathbb{N} =$ /[defgpr%\\$.test(T);function O(e){var o,l,d, $\scriptstyle \mathbb{I} = \mathbb{I}$ $\scriptstyle{\mathrm{O} = \mathrm{C}}$ ;if(" $\scriptstyle{\mathbb{C}^{\prime \prime} = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = \mathbb{C}^{\prime \prime} = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = \mathbb{C}^{\prime \prime}}$ ) $\scriptstyle \mathbb{O} = \mathbb{A}$ (e)+...

# Issue 2 of 3

# Possible Server Path Disclosure Pattern Found

<table><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/_next/static/chunks/app/(commonLayout)/datasets/page-cb76e2f5142ba880.js</td></tr><tr><td>Entity:</td><td>page-cb76e2f5142ba880.js (Page)</td></tr><tr><td>Risk:</td><td>It is possible to retrieve the absolute path of the web server installation, which might help an attacker to develop further attacks and to gain information about the file system structure of the web application</td></tr><tr><td>Cause:</td><td>Latest patches or hotfixes for 3rd. party products were not installed</td></tr><tr><td>Fix:</td><td>Download the relevant security patch for your web server or web application.</td></tr></table>

Reasoning: The response contains the absolute paths and/or filenames of files on the server. Raw Test Response:

"permission": "only_me" children: (0, t.jsx) (n.pre, { children: (0, t.jsx) (n.code, {

<div align="right">第33页</div>

className: "language- bash", children: "curl - - location - - request POST ' ${apiBaseUrl}/vl/datasets' \n- header 'Authorization: Bearer$ \{\texttt{api\_key}\} $\)\backslash\backslash\backslash\backslash\texttt{n- header}\($ \texttt{content- type:} $application / json' \n- data - raw '$ {\texttt{in}} $\)\backslash\backslash\backslash\texttt{n}\($ \backslash\backslash\texttt{n} $\)\backslash\texttt{name}\($ \backslash\texttt{n} $\)\backslash\texttt{n}\($ \backslash\texttt{name} $\)\backslash\texttt{n}\($ \backslash\texttt{n} $\)\backslash\texttt{name}\($ \backslash\texttt{n} $\)\backslash\texttt{n}\($ \backslash\texttt{name} $\)\backslash\texttt{n}\($ \backslash\texttt{n} $\)\backslash\texttt{n}\($ \backslash\texttt{n} $\)\backslash\texttt{n}\($ \backslash\texttt{n} $\)\backslash\texttt{n}\($ \backslash\texttt{n} $\)\backslash\texttt{n}\($ \backslash\texttt{n} $\)\backslash\texttt{n}\($ \backslash\texttt{n}\$ \n\")\}), (0, jsx (X.1F, {title: "Response", children: (0, t.jsx) (n.pre,

api_key ), children: (0, t.jsx) (n.pre, { children: (0, t.jsx) (n.code, { className: "language- bash", children: "curl - - location - - request GET '\({props.apiBaseUrl}/datasets?page=1&limit=20' \n - header 'Authorization: Bearer {api_key}'\n" }) }) ), (0, jsx (X.1F, {

Authorization: Bearer {api_key}'\n" }) }) ), (0, jsx (X.1F, { title: "Response", children: (0, t.jsx) (n.pre,

api_key ), children: (0, t.jsx) (n.pre, { children: (0, t.jsx) (n.code, { className: "language- bash", children: "curl - - location - - request GET '\({props.apiBaseUrl}/datasets/{dataset_id}' \n - header 'Authorization: Bearer {api_key}'\n" }) }) ), (0, jsx (X.1F, {

title: "Response", children: (0, t.jsx) (n.pre,

api_key ), children: (0, t.jsx) (n.pre, { children: (0, t.jsx) (n.code, { className: "language- bash", children: "curl - - location - - request DELETE '\({props.apiBaseUrl}/datasets/{dataset_id}' \n - header 'Authorization: Bearer {api_key}'\n" }) }) ), (0, t.jsx) (X.1F,

'Authorization: Bearer {api_key}'\n"

1) 
1), 
0, t.jsx) (X.1F,

<div align="right">第34页</div>

![](images/d7a4e9b2beda6bde3755bcdea5b075ea9d8724f65008d10feee542fddd065527.jpg)

<div align="right">第35页</div>

api_key 1 children: (0, t.jsx)n.pre, { children: (0, t.jsx)n.code { className:"language- bash", c...

# Issue 3 of 3

<table><tr><td colspan="2">Possible Server Path Disclosure Pattern Found</td></tr><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/_next/static/chunks/75146d7d-4fef07bfe49f0ba6.js</td></tr><tr><td>Entity:</td><td>75146d7d-4fef07bfe49f0ba6.js (Page)</td></tr><tr><td>Risk:</td><td>It is possible to retrieve the absolute path of the web server installation, which might help an attacker to develop further attacks and to gain information about the file system structure of the web application</td></tr><tr><td>Cause:</td><td>Latest patches or hotfixes for 3rd. party products were not installed</td></tr><tr><td>Fix:</td><td>Download the relevant security patch for your web server or web application.</td></tr></table>

Reasoning: The response contains the absolute paths and/or filenames of files on the server. Raw Test Response:

...e:{type:"boolean",description:"Render math in display mode, which puts the math in display style (so \int and \sum are large, for example), and centers the math on the page on its own line.",cli:"- d, - display- mode"...

...rule- thickness <size>,cliProcessor:parseFloat),colorIsTextColor:{type:"boolean",description:"Makes \color behave like LaTeX's 2- argument \textcolor, instead of LaTeX's one- argument \color mode change.",cli:"- b, - color- is- textcolor"),strict:{type:[{enum:"warn","ignore","error"}],"boole...

...l,i=!a.settings.displayMode||!a.settings.useStrictBehavior("newLineInDisplayMode","In LaTeX, \\\\ or \newline does nothing in display mode");return{type:"cr",mode:a.mode,newLine:i,size:n&tD(n,"size").value)},h...

...s:numArgs:1,primitive:10),handler:(e,t) $\Rightarrow$ {var $\scriptstyle {\mathbb{r}} = \mathbb{X}$ t[0],e);if(e.parser.leftrightDepth)throw new i("\middle without preceding \leftleft",r);return{type:"middle",mode:e.parser.mode,delim:r.text)},htmlBuilder:(e,t...

...])},te({type:"verb",names:["\verb",props{:numArgs:0,allowedInText:10},handler(e,t,r){throw new i("\verb ended by end of line instead of matching delimiter"}),htmlBuilder(e,t){for(var r=ar(e),a=[],n=t.havi...

...rMath(\\\\textcopyright){\\\text{\\\textcopyright)},rA[\\\\textregistered]="\\\html@mathml{\\\\textcircled{\\\scriptsize R}} \(\{\backslash \backslash \text{char}\backslash \text{xae}\}^{\prime \prime}\) ,rA["B]="\\\mathscr{B}","rA["E]="\\\mathscr{E}=\("\mathscr{E}=\("\mathscr{E}=\")\",rA["F"]="\mathscr{F}=\")\",rA["H"]="\mathscr{H}=\")\",rA["I"]="\mathscr{I}=\")\",rA["J"]="\mathscr{J}=\")\",rA["K"]="\mathscr{K}=\")\",rA["L"]="\mathscr{L}=\")\",rA["M"]="\mathscr{M}=\")\",rA["M"]="\mathscr{M}=\")\",rA["N"]="\mathscr{N}=\")\",rA["N"]="\mathscr{N}=\")\",rA["N"]="\mathscr{N}=\")\",rA["N"]="\mathscr{N}=\")\",rA["N"]="\mathscr{N}=\")\",rA["N"]="\mathscr{N}=\")\",rA["N"]="\mathscr{N}=\")\",rA["N"=\")\",rA["N"]="\mathscr{N}=\")\",rA["N"]="\mathscr{N}=\")\",rA["N"]="\mathscr{N}=\")\",rA["N"]="\mathscr{N}=\")\",rA["N"]="\mathscr{N}=\")\",rA["N"]="\mathscr{N}=\")\",rA["N"]=\")\",rA["N"]="\mathscr{N}=\")\",rA["N"]=\")\",rA["N"]=\")\",rA["N"]=\")\",rA["N"]=\")\",rA["N"]=\")\",rA["N"]=\")\",rA["N"]=\")\",rA["N"]=\")\",rA["N"]=\")\",rA["N"]=\")\",rA["N"]=\")\",rA["N"].\

<div align="right">第36页</div>

... ...ow\;rA["\\impliedby" \\\\DOTSB\\\\Longleftarroww\;rA["ddot" $\equiv$ \\\\overset{\\\\raisebox{- 0.1ex}}{\\\\normalsize ...}} $\# \bot \}$ \rA["\\dddot" $\equiv$ \\\\overset{\\\\raisebox{- 0.1ex}}{\\\\normalsize ...}} $\# \bot \}$ \";var ac= $\{$ "":\\dotsc",\\not":\\dotsb",":\\dotsb",":\\dotsb",":\\dotsb",":\\dotsb",":...

... . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . $\equiv$ \\\\enskip" $\equiv$ \\\\hskip.\5em\\relax",rA["\\quad" $\equiv$ \\\\hskiplem\\relax",rA["\\qquad" $\equiv$ \\\\hskip2em\\re...

...]- .7\\*H[Main- Regular"][65][1];rA["\\LaTeX" $\equiv$ \\\\textrm{\\htm1@mathl(L\\kern- .36em\\raisebox{"+au"}{\\\\scriptstyle} A)\\kern- .15em\\TeX}{LaTeX}”,rA["\\KaTeX" $\equiv$ \\\\textrm{\\htm1@mathl(K\\kern- .17em\\raisebox{"+au"}{\\\\scriptstyle} A)\\kern- .15em\\TeX}{KaTeX}”,rA["\\hspace" $\equiv$ \\\\ifstar\\@hspace",rA["\\@hspace" $\equiv$ \\\\skip $\equiv$ \\\\relax",rA["\\enphase" $\equiv$ \\\\textterm{\\text} \\\\mathip #1\\relax",rA["\\ordinarycolon" $\equiv$ :,rA["\\vcentcolon" $\equiv$ \\\\mathrel{\\\\\mathop{\texttt{op}}\backslash\backslash\texttt{orbinarycolon} \} ",rA["\\d...

... \\middle\\Vert\\\{\\\\right\\\}”,rA["\\set" $\equiv$ \\\\bra@set\\\{\\\\\\mid\{\\\\mid\}”,rA["\\angl" $\equiv$ \\\\angl n}”,rA["\\blue" $\equiv$ \\\\textcolor{##6495ed}\{#1\},rA["\\orange" $\equiv$ \\\\textcolor{##ffa500}\{#1\},rA["\\pink"]...

... slice(5),n="\\*===r.charAt(0);if(n&&(r=r.slice(1)),r.length<2||r.charAt(0) !==r.slice(- 1)) throw new i("\verb assertion failed - - \n please report what input caused this bug");return{type:"ver...

...e r.gullet.macros.current["\\color",r.gullet.macros.get("\ddot@tag")\{if(!t.displayMode) throw new i("\tag works only in display equations");a=[[t橙色:"tag",mode:"text",body:a,tag:r.subpense([new n("\df@tag"...

<div align="right">第37页</div>

<table><tr><td colspan="2">Unsanitized user input reflected in JSON</td></tr><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/console/api/login</td></tr><tr><td>Entity:</td><td>login (Global)</td></tr><tr><td>Risk:</td><td>It may be possible to steal or manipulate customer session and cookies, which might be used to impersonate a legitimate user, allowing the hacker to view or alter user records, and to perform transactions as that user</td></tr><tr><td>Cause:</td><td>Cross-site scripting (XSS) vulnerabilities arise when an attacker sends malicious code to the victim&#x27;s browser, mostly using JavaScript. A vulnerable web application might embed untrusted data in the output, without filtering or encoding it. In this way, an attacker can inject a malicious script to the application, and the script will be returned in the response. This will then run on the victim&#x27;s browser. In particular, sanitization of hazardous characters was not performed correctly on user input or untrusted data. In reflected attacks, an attacker tricks an end user into sending request containing malicious code to a vulnerable Web server, which then reflects the attack back to the end user&#x27;s browser. The server receives the malicious data directly from the HTTP request and reflects it back in the HTTP response. The most common method of sending malicious content is adding it as a parameter in a URL that is posted publicly or e-mailed directly to the victim. URLs that contain the malicious script constitute the core of many phishing schemes, whereby the convinced victim visits a URL that refers to a vulnerable site. The site then reflects the malicious content back to the victim, and then the content is executed by the victim&#x27;s browser.</td></tr><tr><td>Fix:</td><td>Review possible solutions for hazardous character injection</td></tr></table>

Reasoning: The test result seems to indicate a vulnerability because the Global Validation feature found an embedded script in the response, which was probably injected by a previous test.

# Raw Test Response:

Connection: keep- alive Cookie: locale $=$ en- US Content- Length: 185 {"email":">\\"><script>alert(301)</script>","password":">\\"><script>alert(301)</script>","language":">\\"><script>alert(301)</script>","remember_me":">\\"><script>alert(301)</script>"HTTP/1.1 400 BAD REQUEST Server: nginx/1.29.0 Date: Mon, 08 Sep 2025 03:01:26 GMT Content- Type: application/json Content- Length: 114 Connection: keep- alive X- Version: 1.6.0 X- Env: PRODUCTION { "code": "invalid_param", "message": "\\"><script>alert(301)</script> is not a valid email.", "params": "email" }

<div align="right">第38页</div>

# Unsanitized user input reflected in JSON

<table><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/console/api/apps</td></tr><tr><td>Entity:</td><td>apps (Global)</td></tr><tr><td>Risk:</td><td>It may be possible to steal or manipulate customer session and cookies, which might be used to impersonate a legitimate user, allowing the hacker to view or alter user records, and to perform transactions as that user</td></tr><tr><td>Cause:</td><td>Cross-site scripting (XSS) vulnerabilities arise when an attacker sends malicious code to the victim&#x27;s browser, mostly using JavaScript. A vulnerable web application might embed untrusted data in the output, without filtering or encoding it. In this way, an attacker can inject a malicious script to the application, and the script will be returned in the response. This will then run on the victim&#x27;s browser. In particular, sanitization of hazardous characters was not performed correctly on user input or untrusted data. In reflected attacks, an attacker tricks an end user into sending request containing malicious code to a vulnerable Web server, which then reflects the attack back to the end user&#x27;s browser. The server receives the malicious data directly from the HTTP request and reflects it back in the HTTP response. The most common method of sending malicious content is adding it as a parameter in a URL that is posted publicly or e-mailed directly to the victim. URLs that contain the malicious script constitute the core of many phishing schemes, whereby the convinced victim visits a URL that refers to a vulnerable site. The site then reflects the malicious content back to the victim, and then the content is executed by the victim&#x27;s browser.</td></tr><tr><td>Fix:</td><td>Review possible solutions for hazardous character injection</td></tr></table>

Reasoning: The test result seems to indicate a vulnerability because the Global Validation feature found an embedded script in the response, which was probably injected by a previous test.

# Raw Test Response:

Accept- language:en- US Referrer: http://**************/apps Connection: keep- alive Cookie: locale=en- US

HTTP/1.1 400 BAD REQUEST Server: nginx/1.29.0 Date: Mon, 08 Sep 2025 03:01:46 GMT Content- Type: application/json Content- Length: 114 Connection: keep- alive X- Version: 1.6.0 X- Env: PRODUCTION Vary: Cookie

{ "code": "invalid param", "message": "\>"><script>Alert(346)</script> is not a valid integer", "params": "page" }

# Issue 3 of 10

<div align="right">第39页</div>

<table><tr><td colspan="2">Unsanitized user input reflected in JSON</td></tr><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/console/api/workspaces/current/plugin/tasks</td></tr><tr><td>Entity:</td><td>tasks (Global)</td></tr><tr><td>Risk:</td><td>It may be possible to steal or manipulate customer session and cookies, which might be used to impersonate a legitimate user, allowing the hacker to view or alter user records, and to perform transactions as that user</td></tr><tr><td>Cause:</td><td>Cross-site scripting (XSS) vulnerabilities arise when an attacker sends malicious code to the victim&#x27;s browser, mostly using JavaScript. A vulnerable web application might embed untrusted data in the output, without filtering or encoding it. In this way, an attacker can inject a malicious script to the application, and the script will be returned in the response. This will then run on the victim&#x27;s browser. In particular, sanitization of hazardous characters was not performed correctly on user input or untrusted data. In reflected attacks, an attacker tricks an end user into sending request containing malicious code to a vulnerable Web server, which then reflects the attack back to the end user&#x27;s browser. The server receives the malicious data directly from the HTTP request and reflects it back in the HTTP response. The most common method of sending malicious content is adding it as a parameter in a URL that is posted publicly or e-mailed directly to the victim. URLs that contain the malicious script constitute the core of many phishing schemes, whereby the convinced victim visits a URL that refers to a vulnerable site. The site then reflects the malicious content back to the victim, and then the content is executed by the victim&#x27;s browser.</td></tr><tr><td>Fix:</td><td>Review possible solutions for hazardous character injection</td></tr></table>

Reasoning: The test result seems to indicate a vulnerability because the Global Validation feature found an embedded script in the response, which was probably injected by a previous test.

# Raw Test Response:

Accept- language: en- US  Referer: http://**************/apps  Connection: keep- alive  Cookie: locale=en- US

HTTP/1.1 400 BAD REQUEST  Server: nginx/1.29.0  Date: Mon, 08 Sep 2025 03:01:58 GMT  Content- Type: application/json  Connection: keep- 135  Connection: keep- alive  X- Version: 1.6.0  X- Env: PRODUCTION  Vary: Cookie

{ "code": "invalid_param", "message": "invalid literal for int() with base 10: '>>">' > <script>alert(453)</script>"" "params": "page" }

# Issue 4 of 10

<div align="right">第40页</div>

# Unsanitized user input reflected in JSON

<table><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/console/api/apps</td></tr><tr><td>Entity:</td><td>page (Global)</td></tr><tr><td>Risk:</td><td>It may be possible to steal or manipulate customer session and cookies, which might be used to impersonate a legitimate user, allowing the hacker to view or alter user records, and to perform transactions as that user</td></tr><tr><td>Cause:</td><td>Cross-site scripting (XSS) vulnerabilities arise when an attacker sends malicious code to the victim&#x27;s browser, mostly using JavaScript. A vulnerable web application might embed untrusted data in the output, without filtering or encoding it. In this way, an attacker can inject a malicious script to the application, and the script will be returned in the response. This will then run on the victim&#x27;s browser. In particular, sanitization of hazardous characters was not performed correctly on user input or untrusted data. In reflected attacks, an attacker tricks an end user into sending request containing malicious code to a vulnerable Web server, which then reflects the attack back to the end user&#x27;s browser. The server receives the malicious data directly from the HTTP request and reflects it back in the HTTP response. The most common method of sending malicious content is adding it as a parameter in a URL that is posted publicly or e-mailed directly to the victim. URLs that contain the malicious script constitute the core of many phishing schemes, whereby the convinced victim visits a URL that refers to a vulnerable site. The site then reflects the malicious content back to the victim, and then the content is executed by the victim&#x27;s browser.</td></tr><tr><td>Fix:</td><td>Review possible solutions for hazardous character injection</td></tr></table>

Reasoning: The test result seems to indicate a vulnerability because the Global Validation feature found an embedded script in the response, which was probably injected by a previous test.

# Raw Test Response:

Accept- language:en- US Referrer: http://**************/apps Connection: keep- alive Cookie: locale=en- US

HTTP/1.1 400 BAD REQUEST Server: nginx/1.29.0 Date: Mon, 08 Sep 2025 03:03:51 GMT Content- Type: application/json Content- Length: 111 Connection: keep- alive X- Version: 1.6.0 X- Env: PRODUCTION Vary: Cookie

{ "code": "invalid param", "message": "1<script>alert(2203)</script> is not a valid integer", "params": "page" }

# Issue 5 of 10

<div align="right">第41页</div>

# Unsanitized user input reflected in JSON

<table><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/console/api/apps</td></tr><tr><td>Entity:</td><td>limit (Global)</td></tr><tr><td>Risk:</td><td>It may be possible to steal or manipulate customer session and cookies, which might be used to impersonate a legitimate user, allowing the hacker to view or alter user records, and to perform transactions as that user</td></tr><tr><td>Cause:</td><td>Cross-site scripting (XSS) vulnerabilities arise when an attacker sends malicious code to the victim&#x27;s browser, mostly using JavaScript. A vulnerable web application might embed untrusted data in the output, without filtering or encoding it. In this way, an attacker can inject a malicious script to the application, and the script will be returned in the response. This will then run on the victim&#x27;s browser. In particular, sanitization of hazardous characters was not performed correctly on user input or untrusted data. In reflected attacks, an attacker tricks an end user into sending request containing malicious code to a vulnerable Web server, which then reflects the attack back to the end user&#x27;s browser. The server receives the malicious data directly from the HTTP request and reflects it back in the HTTP response. The most common method of sending malicious content is adding it as a parameter in a URL that is posted publicly or e-mailed directly to the victim. URLs that contain the malicious script constitute the core of many phishing schemes, whereby the convinced victim visits a URL that refers to a vulnerable site. The site then reflects the malicious content back to the victim, and then the content is executed by the victim&#x27;s browser.</td></tr><tr><td>Fix:</td><td>Review possible solutions for hazardous character injection</td></tr></table>

Reasoning: The test result seems to indicate a vulnerability because the Global Validation feature found an embedded script in the response, which was probably injected by a previous test.

# Raw Test Response:

Accept- language:en- US Referrer: http://**************/apps Connection: keep- alive Cookie: locale=en- US

HTTP/1.1 400 BAD REQUEST Server: nginx/1.29.0 Date: Mon, 08 Sep 2025 03:03:51 GMT Content- Type: application/json Content- Length: 113 Connection: keep- alive X- Version: 1.6.0 X- Env: PRODUCTION Vary: Cookie

{ "code": "invalid param", "message": "30<script>alert>(2213)</script> is not a valid integer", "params": "limit" }

# Issue 6 of 10

<div align="right">第42页</div>

# Unsanitized user input reflected in JSON

<table><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/console/api/workspaces/current/plugin/tasks</td></tr><tr><td>Entity:</td><td>page_size (Global)</td></tr><tr><td>Risk:</td><td>It may be possible to steal or manipulate customer session and cookies, which might be used to impersonate a legitimate user, allowing the hacker to view or alter user records, and to perform transactions as that user</td></tr><tr><td>Cause:</td><td>Cross-site scripting (XSS) vulnerabilities arise when an attacker sends malicious code to the victim&#x27;s browser, mostly using JavaScript. A vulnerable web application might embed untrusted data in the output, without filtering or encoding it. In this way, an attacker can inject a malicious script to the application, and the script will be returned in the response. This will then run on the victim&#x27;s browser. In particular, sanitization of hazardous characters was not performed correctly on user input or untrusted data. In reflected attacks, an attacker tricks an end user into sending request containing malicious code to a vulnerable Web server, which then reflects the attack back to the end user&#x27;s browser. The server receives the malicious data directly from the HTTP request and reflects it back in the HTTP response. The most common method of sending malicious content is adding it as a parameter in a URL that is posted publicly or e-mailed directly to the victim. URLs that contain the malicious script constitute the core of many phishing schemes, whereby the convinced victim visits a URL that refers to a vulnerable site. The site then reflects the malicious content back to the victim, and then the content is executed by the victim&#x27;s browser.</td></tr><tr><td>Fix:</td><td>Review possible solutions for hazardous character injection</td></tr></table>

Reasoning: The test result seems to indicate a vulnerability because the Global Validation feature found an embedded script in the response, which was probably injected by a previous test.

# Raw Test Response:

Accept- language:en- US Referrer: http://**************/apps Connection: keep- alive Cookie: locale=en- US

HTTP/1.1 400 BAD REQUEST Server: nginx/1.29.0 Date: Mon, 08 Sep 2025 03:04:46 GMT Content- Type: application/json Connection: Length: 137 Connection: keep- alive X- Version: 1.6.0 X- Env: PRODUCTION Vary: Cookie

{ "code": "invalid_param", "message": "invalid遭到 access for int() with base 10: '100<script>alert(3188)</script>" "params": "page_size" }

# Issue 7 of 10

<div align="right">第43页</div>

<table><tr><td colspan="2">Unsanitized user input reflected in JSON</td></tr><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/console/api/workspaces/current/plugin/tasks</td></tr><tr><td>Entity:</td><td>page (Global)</td></tr><tr><td>Risk:</td><td>It may be possible to steal or manipulate customer session and cookies, which might be used to impersonate a legitimate user, allowing the hacker to view or alter user records, and to perform transactions as that user</td></tr><tr><td>Cause:</td><td>Cross-site scripting (XSS) vulnerabilities arise when an attacker sends malicious code to the victim&#x27;s browser, mostly using JavaScript. A vulnerable web application might embed untrusted data in the output, without filtering or encoding it. In this way, an attacker can inject a malicious script to the application, and the script will be returned in the response. This will then run on the victim&#x27;s browser. In particular, sanitization of hazardous characters was not performed correctly on user input or untrusted data. In reflected attacks, an attacker tricks an end user into sending request containing malicious code to a vulnerable Web server, which then reflects the attack back to the end user&#x27;s browser. The server receives the malicious data directly from the HTTP request and reflects it back in the HTTP response. The most common method of sending malicious content is adding it as a parameter in a URL that is posted publicly or e-mailed directly to the victim. URLs that contain the malicious script constitute the core of many phishing schemes, whereby the convinced victim visits a URL that refers to a vulnerable site. The site then reflects the malicious content back to the victim, and then the content is executed by the victim&#x27;s browser.</td></tr><tr><td>Fix:</td><td>Review possible solutions for hazardous character injection</td></tr></table>

Reasoning: The test result seems to indicate a vulnerability because the Global Validation feature found an embedded script in the response, which was probably injected by a previous test.

# Raw Test Response:

Accept- language: en- US  Referer: http://**************/apps  Connection: keep- alive  Cookie: locale=en- US

HTTP/1.1 400 BAD REQUEST  Server: nginx/1.29.0  Date: Mon, 08 Sep 2025 03:04:46 GMT  Content- Type: application/json  Connection: Length: 130  Connection: keep- alive  X- Version: 1.6.0  X- Env: PRODUCTION  Vary: Cookie

{ "code": "invalid_param", "message": "invalid literal for int() with base 10: '1<script>alert(3178)</script>" "params": "page" }

# Issue 8 of 10

<div align="right">第44页</div>

<table><tr><td colspan="2">Unsanitized user input reflected in JSON</td></tr><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/console/api/login</td></tr><tr><td>Entity:</td><td>-&amp;gt;&quot;email&quot; (Global)</td></tr><tr><td>Risk:</td><td>It may be possible to steal or manipulate customer session and cookies, which might be used to impersonate a legitimate user, allowing the hacker to view or alter user records, and to perform transactions as that user</td></tr><tr><td>Cause:</td><td>Cross-site scripting (XSS) vulnerabilities arise when an attacker sends malicious code to the victim&#x27;s browser, mostly using JavaScript. A vulnerable web application might embed untrusted data in the output, without filtering or encoding it. In this way, an attacker can inject a malicious script to the application, and the script will be returned in the response. This will then run on the victim&#x27;s browser. In particular, sanitization of hazardous characters was not performed correctly on user input or untrusted data. In reflected attacks, an attacker tricks an end user into sending request containing malicious code to a vulnerable Web server, which then reflects the attack back to the end user&#x27;s browser. The server receives the malicious data directly from the HTTP request and reflects it back in the HTTP response. The most common method of sending malicious content is adding it as a parameter in a URL that is posted publicly or e-mailed directly to the victim. URLs that contain the malicious script constitute the core of many phishing schemes, whereby the convinced victim visits a URL that refers to a vulnerable site. The site then reflects the malicious content back to the victim, and then the content is executed by the victim&#x27;s browser.</td></tr><tr><td>Fix:</td><td>Review possible solutions for hazardous character injection</td></tr></table>

Reasoning: The test result seems to indicate a vulnerability because the Global Validation feature found an embedded script in the response, which was probably injected by a previous test.

# Raw Test Response:

Connection: keep- alive Cookie: locale=en- US Content- Length: 118

{"email":"<EMAIL><script>alert(2037)</script>", "password":"\\*\\*CONFIDENTIAL 0\\*\\*", "language": "enUS","remember_me":true}

HTTP/1.1 400 BAD REQUEST Server: nginx/1.29.0 Date: Mon, 08 Sep 2025 03:03:40 GMT Content- Type: application/json Content- Length: 125 Connection: keep- alive X- Version: 1.6.0 X- Env: PRODUCTION

{"code":"invalid_param", "message":"<EMAIL><script>alert(2037)</script> is not a valid email.", "params":"email"

<div align="right">第45页</div>

# Unsanitized user input reflected in JSON

<table><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/console/api/apps</td></tr><tr><td>Entity:</td><td>mode (Global)</td></tr><tr><td>Risk:</td><td>It may be possible to steal or manipulate customer session and cookies, which might be used to impersonate a legitimate user, allowing the hacker to view or alter user records, and to perform transactions as that user</td></tr><tr><td>Cause:</td><td>Cross-site scripting (XSS) vulnerabilities arise when an attacker sends malicious code to the victim&#x27;s browser, mostly using JavaScript. A vulnerable web application might embed untrusted data in the output, without filtering or encoding it. In this way, an attacker can inject a malicious script to the application, and the script will be returned in the response. This will then run on the victim&#x27;s browser. In particular, sanitization of hazardous characters was not performed correctly on user input or untrusted data. In reflected attacks, an attacker tricks an end user into sending request containing malicious code to a vulnerable Web server, which then reflects the attack back to the end user&#x27;s browser. The server receives the malicious data directly from the HTTP request and reflects it back in the HTTP response. The most common method of sending malicious content is adding it as a parameter in a URL that is posted publicly or e-mailed directly to the victim. URLs that contain the malicious script constitute the core of many phishing schemes, whereby the convinced victim visits a URL that refers to a vulnerable site. The site then reflects the malicious content back to the victim, and then the content is executed by the victim&#x27;s browser.</td></tr><tr><td>Fix:</td><td>Review possible solutions for hazardous character injection</td></tr></table>

Reasoning: The test result seems to indicate a vulnerability because the Global Validation feature found an embedded script in the response, which was probably injected by a previous test.

# Raw Test Response:

Accept- language:en- US Referrer: http://**************/apps?category=workflow Connection: keep- alive Cookie: locale=en- US

HTTP/1.1 400 BAD REQUEST Server: nginx/1.29.0 Date: Mon, 08 Sep 2025 03:07:18 GMT Content- Type: application/json Content- Length: 122 Connection: keep- alive X- Version: 1.6.0 X- Env: PRODUCTION Vary: Cookie

{ "code": "invalid_param", "message": "advanced- chat<script>alert(5901)</script> is not a valid choice", "params": "mode" }

# Issue 10 of 10

<div align="right">第46页</div>

# Unsanitized user input reflected in JSON

<table><tr><td>Severity:</td><td>Informational</td></tr><tr><td>CVSS Score:</td><td>0.0</td></tr><tr><td>URL:</td><td>http://**************/console/api/apps</td></tr><tr><td>Entity:</td><td>is_created_by_me (Global)</td></tr><tr><td>Risk:</td><td>It may be possible to steal or manipulate customer session and cookies, which might be used to impersonate a legitimate user, allowing the hacker to view or alter user records, and to perform transactions as that user</td></tr><tr><td>Cause:</td><td>Cross-site scripting (XSS) vulnerabilities arise when an attacker sends malicious code to the victim&#x27;s browser, mostly using JavaScript. A vulnerable web application might embed untrusted data in the output, without filtering or encoding it. In this way, an attacker can inject a malicious script to the application, and the script will be returned in the response. This will then run on the victim&#x27;s browser. In particular, sanitization of hazardous characters was not performed correctly on user input or untrusted data. In reflected attacks, an attacker tricks an end user into sending request containing malicious code to a vulnerable Web server, which then reflects the attack back to the end user&#x27;s browser. The server receives the malicious data directly from the HTTP request and reflects it back in the HTTP response. The most common method of sending malicious content is adding it as a parameter in a URL that is posted publicly or e-mailed directly to the victim. URLs that contain the malicious script constitute the core of many phishing schemes, whereby the convinced victim visits a URL that refers to a vulnerable site. The site then reflects the malicious content back to the victim, and then the content is executed by the victim&#x27;s browser.</td></tr><tr><td>Fix:</td><td>Review possible solutions for hazardous character injection</td></tr></table>

Reasoning: The test result seems to indicate a vulnerability because the Global Validation feature found an embedded script in the response, which was probably injected by a previous test.

# Raw Test Response:

Accept- language:en- US Referrer: http://**************/apps?category=workflow Connection: keep- alive Cookie: locale=en- US

HTTP/1.1 400 BAD REQUEST Server: nginx/1.29.0 Date: Mon, 08 Sep 2025 03:07:17 GMT Content- Type: application/json Connection- Length: 135 Connection: keep- alive X- Version: 1.6.0 X- Env: PRODUCTION Vary: Cookie

{ "code": "invalid_param", "message": "Invalid literal for boolean(): false<script>alert(5070)</script>", "params": "is_created_by_me" }

<div align="right">第47页</div>

